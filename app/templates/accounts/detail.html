{% extends "base.html" %}

{% block title %}账号详情 - {{ account.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2>
                        <i class="fas fa-user-circle"></i> 账号详情
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                            <li class="breadcrumb-item"><a href="{{ url_for('accounts.list_accounts') }}">账号管理</a></li>
                            <li class="breadcrumb-item active">{{ account.name }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ url_for('accounts.edit_account', account_id=account.id) }}" 
                       class="btn btn-primary">
                        <i class="fas fa-edit"></i> 编辑账号
                    </a>
                    <a href="{{ url_for('accounts.list_accounts') }}" 
                       class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>

            <!-- 账号基本信息 -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> 基本信息
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <dl class="row">
                                        <dt class="col-sm-4">账号名称:</dt>
                                        <dd class="col-sm-8">{{ account.name }}</dd>
                                        
                                        <dt class="col-sm-4">用户名:</dt>
                                        <dd class="col-sm-8">{{ account.username or '未设置' }}</dd>
                                        
                                        <dt class="col-sm-4">平台:</dt>
                                        <dd class="col-sm-8">
                                            <span class="badge bg-primary">{{ account.platform }}</span>
                                        </dd>
                                        
                                        <dt class="col-sm-4">账号类型:</dt>
                                        <dd class="col-sm-8">
                                            {% if account.account_type == 'monitor' %}
                                                <span class="badge bg-info">监控账号</span>
                                            {% elif account.account_type == 'upload' %}
                                                <span class="badge bg-success">上传账号</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ account.account_type }}</span>
                                            {% endif %}
                                        </dd>
                                    </dl>
                                </div>
                                <div class="col-md-6">
                                    <dl class="row">
                                        <dt class="col-sm-4">账号状态:</dt>
                                        <dd class="col-sm-8">
                                            {% if account.is_active %}
                                                <span class="badge bg-success">活跃</span>
                                            {% else %}
                                                <span class="badge bg-secondary">暂停</span>
                                            {% endif %}
                                        </dd>
                                        
                                        <dt class="col-sm-4">创建时间:</dt>
                                        <dd class="col-sm-8">{{ account.created_at.strftime('%Y-%m-%d %H:%M:%S') if account.created_at else '未知' }}</dd>
                                        
                                        <dt class="col-sm-4">更新时间:</dt>
                                        <dd class="col-sm-8">{{ account.updated_at.strftime('%Y-%m-%d %H:%M:%S') if account.updated_at else '未知' }}</dd>
                                        
                                        <dt class="col-sm-4">描述:</dt>
                                        <dd class="col-sm-8">{{ account.description or '无' }}</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 账号统计 -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-chart-bar"></i> 使用统计
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <h4 class="text-primary mb-1">{{ related_tasks|length }}</h4>
                                        <small class="text-muted">关联任务</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <h4 class="text-success mb-1">0</h4>
                                    <small class="text-muted">处理视频</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs"></i> 配置信息
                    </h5>
                </div>
                <div class="card-body">
                    {% if account.config %}
                        <div class="row">
                            {% for key, value in account.config.items() %}
                            <div class="col-md-6 mb-3">
                                <strong>{{ key }}:</strong>
                                <div class="text-muted">
                                    {% if key == 'cookies' %}
                                        <code>{{ value[:50] }}{% if value|length > 50 %}...{% endif %}</code>
                                    {% else %}
                                        <code>{{ value }}</code>
                                    {% endif %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted">暂无配置信息</p>
                    {% endif %}
                </div>
            </div>

            <!-- 关联任务 -->
            {% if related_tasks %}
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tasks"></i> 关联任务 ({{ related_tasks|length }})
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>任务名称</th>
                                    <th>关联类型</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in related_tasks %}
                                <tr>
                                    <td>{{ task.name }}</td>
                                    <td>
                                        {% if task.monitor_account_id == account.id %}
                                            <span class="badge bg-info">监控账号</span>
                                        {% endif %}
                                        {% if task.upload_account_id == account.id %}
                                            <span class="badge bg-success">上传账号</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if task.is_active %}
                                            <span class="badge bg-success">活跃</span>
                                        {% else %}
                                            <span class="badge bg-secondary">暂停</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ task.created_at.strftime('%m-%d %H:%M') if task.created_at else '未知' }}</td>
                                    <td>
                                        <a href="{{ url_for('tasks.view_task', task_id=task.id) }}" 
                                           class="btn btn-sm btn-outline-primary">查看</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
